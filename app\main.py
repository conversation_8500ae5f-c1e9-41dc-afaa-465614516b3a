import sys
import asyncio


import logging
import os
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from tortoise import Tortoise
from contextlib import asynccontextmanager
from app.utils.config import settings
from app.api import search, resource, stats, feedback, auth, admin
from app.api import config_advanced
from app.api.admin_feedback import router as admin_feedback_router
from app.db.engine import init_db, close_db
from app.middleware.concurrency import concurrency_limiter
from app.crawlers.panku8_crawler import panku8_crawler
from app.middleware.verify import verify_origin
from app.services.baidu_pan_service import baidu_pan_service
from app.services.quark_pan_service import quark_pan_service

log_level_str = settings.get("logging.level", "INFO")
log_level = getattr(logging, log_level_str.upper(), logging.INFO)

root_logger = logging.getLogger()

for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)
root_logger.setLevel(log_level)

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setLevel(log_level)
console_format = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
console_handler.setFormatter(console_format)
root_logger.addHandler(console_handler)

# 如果配置了文件输出，添加文件处理器
log_file = settings.get("logging.file")
if log_file:
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(log_level)
    file_format = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_format)
    root_logger.addHandler(file_handler)

logger = logging.getLogger("pan-search-api")


# 生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("网盘搜索API服务启动")

    # 初始化数据库连接
    await init_db()
    # 在应用启动时，我们不再自动生成schema。
    # 数据库表的创建和修改应完全由 Aerich 迁移工具管理。
    # await Tortoise.generate_schemas()
    # logger.info("数据库 schema 已同步。")

    # 初始化爬虫和网盘服务
    await panku8_crawler.initialize()
    await baidu_pan_service.initialize()
    await quark_pan_service.initialize()

    logger.info("已初始化爬虫和网盘服务")

    yield

    # 关闭数据库连接
    await close_db()
    logger.info("已关闭数据库连接")
    await panku8_crawler.close()
    # await kdocs_crawler.close()
    if baidu_pan_service.session:
        await baidu_pan_service.session.aclose()
    if quark_pan_service.session:
        await quark_pan_service.session.aclose()
    logger.info("已关闭服务连接池")


app = FastAPI(title="网盘搜索API", lifespan=lifespan)

# CORS 配置
allowed_origins = settings.get("allowed_origins", ["*"])
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"全局异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"status": "error", "message": f"服务器错误: {str(exc)}"},
    )


# 注册中间件
app.middleware("http")(concurrency_limiter)


# 注册根路由
@app.get("/")
async def root():
    return {"message": "网盘搜索API服务正常运行"}


# IndexNow 密钥文件托管
@app.get("/{key_file_name}")
async def get_indexnow_key(key_file_name: str):
    """
    托管 IndexNow API 密钥文件。
    当搜索引擎访问 /<your_api_key_file>.txt 时，
    此端点会返回密钥内容以验证域名所有权。
    """
    key_file_config = settings.get("seo.indexnow_key_file_name")
    api_key = settings.get("seo.indexnow_api_key")

    if key_file_name == key_file_config and api_key:
        return Response(content=api_key, media_type="text/plain")
    return Response(status_code=404)


# 注册API路由
app.include_router(auth.router, prefix="/api/auth")  # 认证路由不需要verify_origin中间件
app.include_router(admin.router, prefix="/api/admin")  # 管理员路由

app.include_router(config_advanced.router, prefix="/api/admin")  # 高级配置管理路由
app.include_router(admin_feedback_router, prefix="/api/admin")  # 资源反馈管理路由
app.include_router(search.router, prefix="/api", dependencies=[Depends(verify_origin)])
app.include_router(
    resource.router, prefix="/api", dependencies=[Depends(verify_origin)]
)
app.include_router(stats.router, prefix="/api", dependencies=[Depends(verify_origin)])
app.include_router(
    feedback.router, prefix="/api", dependencies=[Depends(verify_origin)]
)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app.main:app", host="127.0.0.1", port=9999, reload=True)
