"""
积分系统服务
"""
from typing import List, Optional, Dict, Any
from tortoise.queryset import Q
from tortoise.transactions import in_transaction
import logging

from app.models.user import User, PointsTransaction

logger = logging.getLogger(__name__)


class PointsService:
    """积分系统服务类"""
    
    # 积分规则配置
    POINTS_RULES = {
        "register": 10,           # 新用户注册
        "help_request": -2,       # 发布求助
        "help_answer": 1,         # 回答问题
        "answer_accepted": 5,     # 回答被采纳（回答者和求助者各得5分）
    }
    
    @classmethod
    async def add_points(
        cls, 
        user: User, 
        amount: int, 
        transaction_type: str, 
        description: str, 
        related_id: Optional[int] = None
    ) -> PointsTransaction:
        """
        为用户增加积分
        
        Args:
            user: 用户对象
            amount: 积分数量
            transaction_type: 交易类型
            description: 描述
            related_id: 关联的业务ID
            
        Returns:
            PointsTransaction: 积分变动记录
        """
        async with in_transaction():
            # 更新用户积分
            user.points += amount
            await user.save()
            
            # 创建积分变动记录
            transaction = await PointsTransaction.create(
                user=user,
                amount=amount,
                balance_after=user.points,
                transaction_type=transaction_type,
                description=description,
                related_id=related_id
            )
            
            logger.info(f"用户 {user.username} 获得 {amount} 积分，当前积分：{user.points}")
            return transaction
    
    @classmethod
    async def deduct_points(
        cls, 
        user: User, 
        amount: int, 
        transaction_type: str, 
        description: str, 
        related_id: Optional[int] = None
    ) -> PointsTransaction:
        """
        扣除用户积分
        
        Args:
            user: 用户对象
            amount: 积分数量
            transaction_type: 交易类型
            description: 描述
            related_id: 关联的业务ID
            
        Returns:
            PointsTransaction: 积分变动记录
            
        Raises:
            ValueError: 积分不足时抛出异常
        """
        if user.points < amount:
            raise ValueError(f"积分不足，当前积分：{user.points}，需要扣除：{amount}")
        
        async with in_transaction():
            # 更新用户积分
            user.points -= amount
            await user.save()
            
            # 创建积分变动记录
            transaction = await PointsTransaction.create(
                user=user,
                amount=-amount,
                balance_after=user.points,
                transaction_type=transaction_type,
                description=description,
                related_id=related_id
            )
            
            logger.info(f"用户 {user.username} 扣除 {amount} 积分，当前积分：{user.points}")
            return transaction
    
    @classmethod
    async def register_bonus(cls, user: User) -> PointsTransaction:
        """新用户注册奖励"""
        amount = cls.POINTS_RULES["register"]
        return await cls.add_points(
            user=user,
            amount=amount,
            transaction_type="register",
            description="新用户注册奖励"
        )
    
    @classmethod
    async def help_request_cost(cls, user: User, help_request_id: int) -> PointsTransaction:
        """发布求助扣费"""
        amount = abs(cls.POINTS_RULES["help_request"])
        return await cls.deduct_points(
            user=user,
            amount=amount,
            transaction_type="help_request",
            description="发布求助扣费",
            related_id=help_request_id
        )
    
    @classmethod
    async def help_answer_reward(cls, user: User, answer_id: int) -> PointsTransaction:
        """回答问题奖励"""
        amount = cls.POINTS_RULES["help_answer"]
        return await cls.add_points(
            user=user,
            amount=amount,
            transaction_type="help_answer",
            description="回答问题奖励",
            related_id=answer_id
        )
    
    @classmethod
    async def answer_accepted_reward(cls, answerer: User, requester: User, answer_id: int) -> List[PointsTransaction]:
        """回答被采纳奖励（回答者和求助者各得5分）"""
        amount = cls.POINTS_RULES["answer_accepted"]
        transactions = []
        
        # 回答者奖励
        answerer_transaction = await cls.add_points(
            user=answerer,
            amount=amount,
            transaction_type="answer_accepted",
            description="回答被采纳奖励",
            related_id=answer_id
        )
        transactions.append(answerer_transaction)
        
        # 求助者奖励
        requester_transaction = await cls.add_points(
            user=requester,
            amount=amount,
            transaction_type="answer_accepted",
            description="采纳回答奖励",
            related_id=answer_id
        )
        transactions.append(requester_transaction)
        
        return transactions
    
    @classmethod
    async def admin_adjust_points(
        cls, 
        user: User, 
        amount: int, 
        description: str, 
        admin_user: User
    ) -> PointsTransaction:
        """管理员调整积分"""
        if amount > 0:
            transaction = await cls.add_points(
                user=user,
                amount=amount,
                transaction_type="admin_adjust",
                description=f"管理员调整：{description}"
            )
        else:
            transaction = await cls.deduct_points(
                user=user,
                amount=abs(amount),
                transaction_type="admin_adjust",
                description=f"管理员调整：{description}"
            )
        
        logger.info(f"管理员 {admin_user.username} 为用户 {user.username} 调整积分 {amount}")
        return transaction
    
    @classmethod
    async def get_user_points_history(
        cls, 
        user: User, 
        page: int = 1, 
        size: int = 20,
        transaction_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取用户积分历史"""
        query = PointsTransaction.filter(user=user)
        
        if transaction_type:
            query = query.filter(transaction_type=transaction_type)
        
        # 计算总数
        total = await query.count()
        
        # 分页查询
        offset = (page - 1) * size
        transactions = await query.offset(offset).limit(size).order_by("-created_at")
        
        return {
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size,
            "transactions": transactions
        }
    
    @classmethod
    async def get_points_statistics(cls) -> Dict[str, Any]:
        """获取积分系统统计信息"""
        # 总积分发放量
        total_issued = await PointsTransaction.filter(amount__gt=0).aggregate(
            total={"sum": "amount"}
        )
        
        # 总积分消耗量
        total_consumed = await PointsTransaction.filter(amount__lt=0).aggregate(
            total={"sum": "amount"}
        )
        
        # 按交易类型统计
        type_stats = {}
        for transaction_type in cls.POINTS_RULES.keys():
            count = await PointsTransaction.filter(transaction_type=transaction_type).count()
            amount_sum = await PointsTransaction.filter(transaction_type=transaction_type).aggregate(
                total={"sum": "amount"}
            )
            type_stats[transaction_type] = {
                "count": count,
                "total_amount": amount_sum.get("total", 0) or 0
            }
        
        # 用户积分排行榜（前10名）
        top_users = await User.all().order_by("-points").limit(10).values(
            "id", "username", "nickname", "points"
        )
        
        return {
            "total_issued": total_issued.get("total", 0) or 0,
            "total_consumed": abs(total_consumed.get("total", 0) or 0),
            "type_statistics": type_stats,
            "top_users": top_users
        }
    
    @classmethod
    def get_title_by_points(cls, points: int) -> str:
        """根据积分获取用户头衔"""
        if points >= 2500:
            return "大师"
        elif points >= 1000:
            return "专家"
        elif points >= 500:
            return "高级"
        elif points >= 200:
            return "中级"
        elif points >= 50:
            return "初级"
        else:
            return "新手"
