"""
资源求助系统API接口
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi import status as http_status
from typing import Optional
import logging

from app.models.user import User
from app.models.help_request import (
    HelpRequest,
    HelpAnswer,
    ResourceType,
    HelpRequestStatus,
)
from app.models.help_request_models import (
    HelpRequestCreateRequest,
    HelpAnswerCreateRequest,
    UserBasicInfo,
)
from app.services.help_request_service import HelpRequestService
from app.services.points_service import PointsService
from app.core.permissions import (
    get_current_user,
    get_current_admin_user,
    get_current_user_optional,
)
from app.models.auth_models import ApiResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/help", tags=["资源求助"])


@router.post("/requests", response_model=ApiResponse)
async def create_help_request(
    request: HelpRequestCreateRequest, current_user: User = Depends(get_current_user)
):
    """创建求助请求"""
    try:
        help_request = await HelpRequestService.create_help_request(
            user=current_user,
            title=request.title,
            description=request.description,
            cloud_disk_types=[
                disk_type.value for disk_type in request.cloud_disk_types
            ],
            resource_type=request.resource_type.value,
        )

        return ApiResponse(
            status="success",
            message="求助发布成功",
            data={
                "request_id": help_request.id,
                "title": help_request.title,
                "points_deducted": 2,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=http_status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"创建求助失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建求助失败",
        )


@router.get("/requests", response_model=ApiResponse)
async def get_help_requests(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="状态筛选"),
    resource_type: Optional[str] = Query(None, description="资源类型筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: Optional[User] = Depends(get_current_user_optional),
):
    """获取求助列表"""
    try:
        result = await HelpRequestService.get_help_requests(
            page=page,
            size=size,
            status=status,
            resource_type=resource_type,
            search=search,
        )

        # 转换为响应格式
        requests_data = []
        for req in result["requests"]:
            user_info = UserBasicInfo(
                id=req.requester.id,
                username=req.requester.username,
                nickname=req.requester.nickname,
                points=req.requester.points,
                title=PointsService.get_title_by_points(req.requester.points),
            )

            requests_data.append(
                {
                    "id": req.id,
                    "title": req.title,
                    "description": req.description,
                    "cloud_disk_types": req.cloud_disk_types,
                    "resource_type": req.resource_type,
                    "status": req.status,
                    "requester": user_info.model_dump(),
                    "answer_count": req.answer_count,
                    "view_count": req.view_count,
                    "created_at": req.created_at.isoformat(),
                    "updated_at": req.updated_at.isoformat(),
                    "resolved_at": (
                        req.resolved_at.isoformat() if req.resolved_at else None
                    ),
                }
            )

        response_data = {
            "total": result["total"],
            "page": result["page"],
            "size": result["size"],
            "pages": result["pages"],
            "requests": requests_data,
        }

        return ApiResponse(
            status="success", message="获取求助列表成功", data=response_data
        )
    except Exception as e:
        logger.error(f"获取求助列表失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取求助列表失败",
        )


@router.get("/statistics", response_model=ApiResponse)
async def get_help_statistics():
    """获取求助系统统计信息"""
    try:
        stats = await HelpRequestService.get_help_request_statistics()

        return ApiResponse(status="success", message="获取统计信息成功", data=stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败",
        )
