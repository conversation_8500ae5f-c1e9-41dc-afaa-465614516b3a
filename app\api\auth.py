from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import HTTPAuthorizationCredentials
from tortoise.exceptions import IntegrityError
import logging
from datetime import datetime

from app.models.auth_models import (
    UserRegisterRequest,
    UserLoginRequest,
    UserLoginResponse,
    TokenRefreshRequest,
    TokenRefreshResponse,
    ForgotPasswordRequest,
    ResetPasswordRequest,
    ChangePasswordRequest,
    EmailVerificationRequest,
    ResendVerificationRequest,
    UserProfileResponse,
    UserProfileUpdateRequest,
    ApiResponse,
)
from app.models.user import User, Role
from app.core.auth import AuthService
from app.core.email import email_service
from app.core.permissions import get_current_user, get_current_active_user, security
from app.services.points_service import PointsService

logger = logging.getLogger("auth-api")
router = APIRouter(tags=["认证"])


@router.post(
    "/register",
    response_model=ApiResponse,
    summary="用户注册",
    description="用户注册，需要邮箱验证",
)
async def register(user_data: UserRegisterRequest):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        existing_username = await User.filter(username=user_data.username).first()
        if existing_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="用户名已存在"
            )

        # 检查邮箱是否已存在
        existing_email = await User.filter(email=user_data.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="邮箱已被注册"
            )

        # 获取默认用户角色
        user_role = await Role.filter(name="user").first()
        if not user_role:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="系统角色配置错误",
            )

        # 创建用户
        user = User(
            username=user_data.username,
            email=user_data.email,
            nickname=user_data.nickname or user_data.username,
            role=user_role,
            status="pending",  # 待邮箱验证
        )
        user.set_password(user_data.password)

        # 生成邮箱验证令牌
        verify_token = user.generate_email_verify_token()

        await user.save()

        # 给新用户注册积分奖励
        try:
            await PointsService.register_bonus(user)
            logger.info(f"新用户 {user.username} 获得注册积分奖励")
        except Exception as e:
            logger.error(f"给用户 {user.username} 发放注册积分失败: {e}")
            # 不阻止注册流程，但记录错误

        # 发送验证邮件
        try:
            await email_service.send_verification_email(
                to_email=user.email, username=user.username, verify_token=verify_token
            )
        except Exception as e:
            logger.error(f"发送验证邮件失败: {e}")
            # 不阻止注册流程，但记录错误

        return ApiResponse(
            status="success",
            message="注册成功，请检查邮箱并点击验证链接激活账户",
            data={"user_id": user.id},
        )

    except IntegrityError as e:
        logger.error(f"用户注册数据库错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="注册失败，请检查输入信息"
        )
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="注册失败，请稍后重试",
        )


@router.post(
    "/login",
    response_model=UserLoginResponse,
    summary="用户登录",
    description="用户登录，返回访问令牌",
)
async def login(login_data: UserLoginRequest):
    """用户登录"""
    try:
        # 认证用户
        user = await AuthService.authenticate_user(
            login_data.username, login_data.password
        )

        # 检查邮箱验证状态
        if not user.email_verified and user.status == "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="请先验证邮箱后再登录"
            )

        # 创建令牌
        tokens = await AuthService.create_user_tokens(user)

        # 构建用户信息
        await user.fetch_related("role")
        user_data = user.__dict__.copy()
        user_data["title"] = PointsService.get_title_by_points(user.points)
        user_profile = UserProfileResponse.model_validate(user_data)

        return UserLoginResponse(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            token_type=tokens["token_type"],
            expires_in=tokens["expires_in"],
            user=user_profile,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录失败，请稍后重试",
        )


@router.post(
    "/logout",
    response_model=ApiResponse,
    summary="用户登出",
    description="用户登出，使刷新令牌失效",
)
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="未提供认证令牌"
        )

    try:
        # 这里需要从请求中获取refresh_token
        # 由于HTTP Bearer只能传access_token，实际项目中可能需要调整
        # 暂时使用access_token来查找对应的session
        from app.core.auth import JWTService

        payload = JWTService.verify_token(credentials.credentials)
        user_id = payload.get("user_id")

        # 登出所有会话（简化处理）
        await AuthService.logout_all_sessions(user_id)

        return ApiResponse(status="success", message="登出成功")

    except Exception as e:
        logger.error(f"用户登出失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="登出失败"
        )


@router.post(
    "/refresh",
    response_model=TokenRefreshResponse,
    summary="刷新令牌",
    description="使用刷新令牌获取新的访问令牌",
)
async def refresh_token(refresh_data: TokenRefreshRequest):
    """刷新访问令牌"""
    try:
        tokens = await AuthService.refresh_access_token(refresh_data.refresh_token)

        return TokenRefreshResponse(
            access_token=tokens["access_token"],
            token_type=tokens["token_type"],
            expires_in=tokens["expires_in"],
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌刷新失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="令牌刷新失败"
        )


@router.post(
    "/verify-email",
    response_model=ApiResponse,
    summary="验证邮箱",
    description="验证用户邮箱地址",
)
async def verify_email(verify_data: EmailVerificationRequest):
    """验证邮箱"""
    try:
        # 查找用户
        user = await User.filter(email_verify_token__isnull=False).first()

        if not user or not user.verify_email_token(verify_data.token):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="验证令牌无效或已过期"
            )

        # 激活用户
        user.email_verified = True
        user.status = "active"
        user.email_verify_token = None
        user.email_verify_expires = None
        await user.save()

        return ApiResponse(status="success", message="邮箱验证成功，账户已激活")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮箱验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="邮箱验证失败"
        )


@router.post(
    "/forgot-password",
    response_model=ApiResponse,
    summary="忘记密码",
    description="发送密码重置邮件",
)
async def forgot_password(forgot_data: ForgotPasswordRequest):
    """忘记密码"""
    try:
        # 查找用户
        user = await User.filter(email=forgot_data.email).first()

        if not user:
            # 为了安全，不透露用户是否存在
            return ApiResponse(status="success", message="如果邮箱存在，重置链接已发送")

        # 生成重置令牌
        reset_token = user.generate_password_reset_token()
        await user.save()

        # 发送重置邮件
        try:
            await email_service.send_password_reset_email(
                to_email=user.email, username=user.username, reset_token=reset_token
            )
        except Exception as e:
            logger.error(f"发送密码重置邮件失败: {e}")

        return ApiResponse(status="success", message="如果邮箱存在，重置链接已发送")

    except Exception as e:
        logger.error(f"忘记密码处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="处理失败，请稍后重试",
        )


@router.post(
    "/reset-password",
    response_model=ApiResponse,
    summary="重置密码",
    description="使用重置令牌重置密码",
)
async def reset_password(reset_data: ResetPasswordRequest):
    """重置密码"""
    try:
        # 查找用户
        user = await User.filter(password_reset_token__isnull=False).first()

        if not user or not user.verify_password_reset_token(reset_data.token):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="重置令牌无效或已过期"
            )

        # 重置密码
        user.set_password(reset_data.new_password)
        user.password_reset_token = None
        user.password_reset_expires = None
        user.unlock_account()  # 解锁账户
        await user.save()

        # 登出所有会话
        await AuthService.logout_all_sessions(user.id)

        return ApiResponse(status="success", message="密码重置成功，请重新登录")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"密码重置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="密码重置失败"
        )


@router.get(
    "/profile",
    response_model=UserProfileResponse,
    summary="获取用户资料",
    description="获取当前用户的个人资料",
)
async def get_profile(current_user: User = Depends(get_current_active_user)):
    """获取用户资料"""
    await current_user.fetch_related("role")

    # 创建用户资料响应，包含积分和头衔信息
    user_data = current_user.__dict__.copy()
    user_data["title"] = PointsService.get_title_by_points(current_user.points)

    return UserProfileResponse.model_validate(user_data)


@router.put(
    "/profile",
    response_model=UserProfileResponse,
    summary="更新用户资料",
    description="更新当前用户的个人资料",
)
async def update_profile(
    profile_data: UserProfileUpdateRequest,
    current_user: User = Depends(get_current_active_user),
):
    """更新用户资料"""
    try:
        # 更新用户信息
        if profile_data.nickname is not None:
            current_user.nickname = profile_data.nickname
        if profile_data.phone is not None:
            current_user.phone = profile_data.phone
        if profile_data.avatar is not None:
            current_user.avatar = profile_data.avatar

        await current_user.save()
        await current_user.fetch_related("role")

        # 创建用户资料响应，包含积分和头衔信息
        user_data = current_user.__dict__.copy()
        user_data["title"] = PointsService.get_title_by_points(current_user.points)

        return UserProfileResponse.model_validate(user_data)

    except Exception as e:
        logger.error(f"更新用户资料失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新失败，请稍后重试",
        )


@router.post(
    "/change-password",
    response_model=ApiResponse,
    summary="修改密码",
    description="修改当前用户的密码",
)
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
):
    """修改密码"""
    try:
        # 验证当前密码
        if not current_user.verify_password(password_data.current_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="当前密码错误"
            )

        # 设置新密码
        current_user.set_password(password_data.new_password)
        await current_user.save()

        # 登出所有其他会话
        await AuthService.logout_all_sessions(current_user.id)

        return ApiResponse(status="success", message="密码修改成功，请重新登录")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="修改密码失败"
        )
