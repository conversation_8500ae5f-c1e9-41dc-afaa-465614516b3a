"""
头衔系统API接口
"""
from fastapi import APIRouter, Depends
from typing import List, Dict, Any
import logging

from app.models.user import User
from app.services.points_service import PointsService
from app.core.permissions import get_current_user_optional
from app.models.auth_models import ApiResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/titles", tags=["头衔系统"])


@router.get("/system", response_model=ApiResponse)
async def get_title_system():
    """获取头衔系统信息"""
    try:
        title_system = {
            "levels": [
                {
                    "name": "资源拾荒者",
                    "min_points": 0,
                    "max_points": 49,
                    "color": "#8B8B8B",  # 灰色
                    "description": "刚刚开始的新用户",
                    "benefits": ["可以浏览资源", "可以发布求助"],
                },
                {
                    "name": "云盘勘探员",
                    "min_points": 50,
                    "max_points": 199,
                    "color": "#4CAF50",  # 绿色
                    "description": "有一定经验的用户",
                    "benefits": ["所有新手权限", "回答问题获得更多关注"],
                },
                {
                    "name": "链接炼金师",
                    "min_points": 200,
                    "max_points": 499,
                    "color": "#2196F3",  # 蓝色
                    "description": "经验丰富的活跃用户",
                    "benefits": ["所有初级权限", "可以举报违规内容"],
                },
                {
                    "name": "资源仲裁者",
                    "min_points": 500,
                    "max_points": 999,
                    "color": "#9C27B0",  # 紫色
                    "description": "资深用户，社区贡献者",
                    "benefits": ["所有中级权限", "优先显示回答"],
                },
                {
                    "name": "数据领主",
                    "min_points": 1000,
                    "max_points": 2499,
                    "color": "#FF9800",  # 橙色
                    "description": "专业知识丰富的专家用户",
                    "benefits": ["所有高级权限", "可以编辑他人回答"],
                },
                {
                    "name": "虚拟仓鼠",
                    "min_points": 2500,
                    "max_points": None,
                    "color": "#F44336",  # 红色
                    "description": "社区的顶级贡献者",
                    "benefits": ["所有专家权限", "社区管理权限", "特殊标识"],
                },
            ],
            "point_rules": {
                "register": {
                    "amount": PointsService.POINTS_RULES["register"],
                    "description": "新用户注册奖励",
                },
                "help_request": {
                    "amount": PointsService.POINTS_RULES["help_request"],
                    "description": "发布求助扣费",
                },
                "help_answer": {
                    "amount": PointsService.POINTS_RULES["help_answer"],
                    "description": "回答问题奖励",
                },
                "answer_accepted": {
                    "amount": PointsService.POINTS_RULES["answer_accepted"],
                    "description": "回答被采纳奖励（回答者和求助者各得此分数）",
                },
            },
        }

        return ApiResponse(
            status="success",
            message="获取头衔系统信息成功",
            data=title_system
        )
    except Exception as e:
        logger.error(f"获取头衔系统信息失败: {e}")
        return ApiResponse(
            status="error",
            message="获取头衔系统信息失败",
            data=None
        )


@router.get("/my", response_model=ApiResponse)
async def get_my_title_info(current_user: User = Depends(get_current_user_optional)):
    """获取当前用户的头衔信息"""
    try:
        if not current_user:
            return ApiResponse(
                status="error",
                message="需要登录才能查看头衔信息",
                data=None
            )
        
        current_title = PointsService.get_title_by_points(current_user.points)
        
        # 计算到下一个头衔需要的积分
        next_level_info = None
        if current_user.points < 50:
            next_level_info = {"name": "初级", "required_points": 50, "need_points": 50 - current_user.points}
        elif current_user.points < 200:
            next_level_info = {"name": "中级", "required_points": 200, "need_points": 200 - current_user.points}
        elif current_user.points < 500:
            next_level_info = {"name": "高级", "required_points": 500, "need_points": 500 - current_user.points}
        elif current_user.points < 1000:
            next_level_info = {"name": "专家", "required_points": 1000, "need_points": 1000 - current_user.points}
        elif current_user.points < 2500:
            next_level_info = {"name": "大师", "required_points": 2500, "need_points": 2500 - current_user.points}
        
        title_info = {
            "current_points": current_user.points,
            "current_title": current_title,
            "next_level": next_level_info,
            "progress_percentage": 0
        }
        
        # 计算当前等级的进度百分比
        if current_user.points < 50:
            title_info["progress_percentage"] = (current_user.points / 50) * 100
        elif current_user.points < 200:
            title_info["progress_percentage"] = ((current_user.points - 50) / 150) * 100
        elif current_user.points < 500:
            title_info["progress_percentage"] = ((current_user.points - 200) / 300) * 100
        elif current_user.points < 1000:
            title_info["progress_percentage"] = ((current_user.points - 500) / 500) * 100
        elif current_user.points < 2500:
            title_info["progress_percentage"] = ((current_user.points - 1000) / 1500) * 100
        else:
            title_info["progress_percentage"] = 100
        
        return ApiResponse(
            status="success",
            message="获取头衔信息成功",
            data=title_info
        )
    except Exception as e:
        logger.error(f"获取用户头衔信息失败: {e}")
        return ApiResponse(
            status="error",
            message="获取头衔信息失败",
            data=None
        )


@router.get("/leaderboard", response_model=ApiResponse)
async def get_title_leaderboard():
    """获取头衔排行榜"""
    try:
        # 获取各个头衔等级的用户数量统计
        title_stats = {}
        
        # 统计各个头衔等级的用户数量
        all_users = await User.all()
        for user in all_users:
            title = PointsService.get_title_by_points(user.points)
            if title not in title_stats:
                title_stats[title] = {"count": 0, "users": []}
            title_stats[title]["count"] += 1
            if len(title_stats[title]["users"]) < 5:  # 只显示前5名
                title_stats[title]["users"].append({
                    "username": user.username,
                    "nickname": user.nickname,
                    "points": user.points
                })
        
        # 按头衔等级排序
        title_order = ["大师", "专家", "高级", "中级", "初级", "新手"]
        ordered_stats = {}
        for title in title_order:
            if title in title_stats:
                ordered_stats[title] = title_stats[title]
        
        return ApiResponse(
            status="success",
            message="获取头衔排行榜成功",
            data={
                "title_statistics": ordered_stats,
                "total_users": len(all_users)
            }
        )
    except Exception as e:
        logger.error(f"获取头衔排行榜失败: {e}")
        return ApiResponse(
            status="error",
            message="获取头衔排行榜失败",
            data=None
        )
