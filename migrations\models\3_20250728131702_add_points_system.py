from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "points_transactions" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "amount" INT NOT NULL,
    "balance_after" INT NOT NULL,
    "transaction_type" VARCHAR(50) NOT NULL,
    "description" VARCHAR(255) NOT NULL,
    "related_id" INT,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "points_transactions"."amount" IS '积分变动数量（正数为增加，负数为减少）';
COMMENT ON COLUMN "points_transactions"."balance_after" IS '变动后的积分余额';
COMMENT ON COLUMN "points_transactions"."transaction_type" IS '交易类型：register/help_request/help_answer/answer_accepted/admin_adjust';
COMMENT ON COLUMN "points_transactions"."description" IS '变动描述';
COMMENT ON COLUMN "points_transactions"."related_id" IS '关联的业务ID（如求助ID、回答ID等）';
COMMENT ON COLUMN "points_transactions"."created_at" IS '创建时间';
COMMENT ON COLUMN "points_transactions"."user_id" IS '关联用户';
COMMENT ON TABLE "points_transactions" IS '积分变动记录表';
        ALTER TABLE "users" ADD "points" INT NOT NULL  DEFAULT 10;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "users" DROP COLUMN "points";
        DROP TABLE IF EXISTS "points_transactions";"""
