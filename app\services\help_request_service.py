"""
资源求助系统服务
"""

from typing import List, Optional, Dict, Any
from tortoise.queryset import Q
from tortoise.transactions import in_transaction
import logging
from datetime import datetime

from app.models.user import User
from app.models.help_request import HelpRequest, HelpAnswer, HelpRequestStatus
from app.services.points_service import PointsService

logger = logging.getLogger(__name__)


class HelpRequestService:
    """资源求助系统服务类"""

    @classmethod
    async def create_help_request(
        cls,
        user: User,
        title: str,
        description: Optional[str],
        cloud_disk_types: List[str],
        resource_type: str,
    ) -> HelpRequest:
        """
        创建求助请求

        Args:
            user: 求助用户
            title: 资源名称
            description: 详细描述
            cloud_disk_types: 网盘类型列表
            resource_type: 资源类型

        Returns:
            HelpRequest: 创建的求助请求

        Raises:
            ValueError: 积分不足时抛出异常
        """
        async with in_transaction():
            # 创建求助请求
            help_request = await HelpRequest.create(
                title=title,
                description=description,
                cloud_disk_types=cloud_disk_types,
                resource_type=resource_type,
                requester=user,
            )

            # 扣除发布求助的积分
            await PointsService.help_request_cost(user, help_request.id)

            logger.info(f"用户 {user.username} 创建了求助: {title}")
            return help_request

    @classmethod
    async def get_help_requests(
        cls,
        page: int = 1,
        size: int = 20,
        status: Optional[str] = None,
        resource_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取求助列表

        Args:
            page: 页码
            size: 每页数量
            status: 状态筛选
            resource_type: 资源类型筛选
            search: 搜索关键词

        Returns:
            Dict: 包含求助列表和分页信息的字典
        """
        # 构建查询条件
        query = HelpRequest.all().prefetch_related("requester")

        # 状态筛选
        if status:
            query = query.filter(status=status)

        # 资源类型筛选
        if resource_type:
            query = query.filter(resource_type=resource_type)

        # 搜索关键词
        if search:
            query = query.filter(
                Q(title__icontains=search) | Q(description__icontains=search)
            )

        # 按创建时间倒序排列
        query = query.order_by("-created_at")

        # 计算总数
        total = await query.count()

        # 分页
        offset = (page - 1) * size
        requests = await query.offset(offset).limit(size)

        # 计算总页数
        pages = (total + size - 1) // size

        return {
            "requests": requests,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages,
        }

    @classmethod
    async def get_help_request_detail(cls, request_id: int) -> Optional[HelpRequest]:
        """
        获取求助详情

        Args:
            request_id: 求助ID

        Returns:
            HelpRequest: 求助详情，如果不存在返回None
        """
        help_request = await HelpRequest.get_or_none(id=request_id).prefetch_related(
            "requester", "answers__answerer"
        )

        if help_request:
            # 增加浏览次数
            help_request.view_count += 1
            await help_request.save()

        return help_request

    @classmethod
    async def create_help_answer(
        cls,
        user: User,
        help_request_id: int,
        resource_title: str,
        resource_link: str,
        cloud_disk_type: str,
        additional_info: Optional[str] = None,
        should_archive: bool = False,
    ) -> HelpAnswer:
        """
        创建求助回答

        Args:
            user: 回答用户
            help_request_id: 求助ID
            resource_title: 资源标题
            resource_link: 资源链接
            cloud_disk_type: 网盘类型
            additional_info: 补充说明
            should_archive: 是否入库

        Returns:
            HelpAnswer: 创建的回答
        """
        async with in_transaction():
            # 检查求助是否存在且状态为开放
            help_request = await HelpRequest.get_or_none(id=help_request_id)
            if not help_request:
                raise ValueError("求助不存在")

            if help_request.status != HelpRequestStatus.OPEN:
                raise ValueError("该求助已关闭，无法回答")

            # 检查用户是否是求助者本人
            if help_request.requester_id == user.id:
                raise ValueError("不能回答自己的求助")

            # 创建回答
            answer = await HelpAnswer.create(
                resource_title=resource_title,
                resource_link=resource_link,
                cloud_disk_type=cloud_disk_type,
                additional_info=additional_info,
                should_archive=should_archive,
                help_request=help_request,
                answerer=user,
            )

            # 更新求助的回答数量
            help_request.answer_count += 1
            await help_request.save()

            # 给回答者积分奖励
            await PointsService.help_answer_reward(user, answer.id)

            logger.info(f"用户 {user.username} 回答了求助 {help_request.title}")
            return answer

    @classmethod
    async def accept_answer(cls, user: User, answer_id: int) -> HelpAnswer:
        """
        采纳回答

        Args:
            user: 求助者
            answer_id: 回答ID

        Returns:
            HelpAnswer: 被采纳的回答
        """
        async with in_transaction():
            # 获取回答和关联的求助
            answer = await HelpAnswer.get_or_none(id=answer_id).prefetch_related(
                "help_request", "answerer"
            )

            if not answer:
                raise ValueError("回答不存在")

            # 检查是否是求助者本人
            if answer.help_request.requester_id != user.id:
                raise ValueError("只有求助者可以采纳回答")

            # 检查求助状态
            if answer.help_request.status != HelpRequestStatus.OPEN:
                raise ValueError("该求助已关闭，无法采纳回答")

            # 检查是否已有被采纳的回答
            existing_accepted = await HelpAnswer.filter(
                help_request=answer.help_request, is_accepted=True
            ).exists()

            if existing_accepted:
                raise ValueError("该求助已有被采纳的回答")

            # 标记回答为已采纳
            answer.is_accepted = True
            answer.accepted_at = datetime.utcnow()
            await answer.save()

            # 更新求助状态为已解决
            answer.help_request.status = HelpRequestStatus.RESOLVED
            answer.help_request.resolved_at = datetime.utcnow()
            await answer.help_request.save()

            # 给回答者和求助者积分奖励
            await PointsService.answer_accepted_reward(
                answerer=answer.answerer, requester=user, answer_id=answer.id
            )

            logger.info(f"用户 {user.username} 采纳了回答 {answer.resource_title}")
            return answer

    @classmethod
    async def get_help_requests(
        cls,
        page: int = 1,
        size: int = 20,
        status: Optional[str] = None,
        resource_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> Dict[str, Any]:
        """获取求助列表"""
        query = HelpRequest.all().prefetch_related("requester")

        # 状态筛选
        if status:
            query = query.filter(status=status)

        # 资源类型筛选
        if resource_type:
            query = query.filter(resource_type=resource_type)

        # 搜索
        if search:
            query = query.filter(
                Q(title__icontains=search) | Q(description__icontains=search)
            )

        # 计算总数
        total = await query.count()

        # 分页查询
        offset = (page - 1) * size
        requests = await query.offset(offset).limit(size).order_by("-created_at")

        return {
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size,
            "requests": requests,
        }

    @classmethod
    async def get_help_request_detail(cls, request_id: int) -> Optional[HelpRequest]:
        """获取求助详情"""
        help_request = await HelpRequest.get_or_none(id=request_id).prefetch_related(
            "requester", "answers__answerer"
        )

        if help_request:
            # 增加浏览次数
            help_request.view_count += 1
            await help_request.save()

        return help_request

    @classmethod
    async def get_help_request_statistics(cls) -> Dict[str, Any]:
        """获取求助系统统计信息"""
        # 基本统计
        total_requests = await HelpRequest.all().count()
        open_requests = await HelpRequest.filter(status=HelpRequestStatus.OPEN).count()
        resolved_requests = await HelpRequest.filter(
            status=HelpRequestStatus.RESOLVED
        ).count()
        total_answers = await HelpAnswer.all().count()
        accepted_answers = await HelpAnswer.filter(is_accepted=True).count()

        # 热心用户排行榜（按回答数量）
        from tortoise.functions import Count

        top_helpers_data = (
            await HelpAnswer.annotate(answer_count=Count("id"))
            .group_by("answerer_id")
            .order_by("-answer_count")
            .limit(10)
            .values("answerer_id", "answer_count")
        )

        # 获取用户详细信息
        top_helpers = []
        for helper_data in top_helpers_data:
            user = await User.get(id=helper_data["answerer_id"])
            top_helpers.append(
                {
                    "id": user.id,
                    "username": user.username,
                    "nickname": user.nickname,
                    "points": user.points,
                    "title": PointsService.get_title_by_points(user.points),
                    "answer_count": helper_data["answer_count"],
                }
            )

        # 最近的求助
        recent_requests = (
            await HelpRequest.all()
            .prefetch_related("requester")
            .order_by("-created_at")
            .limit(5)
        )

        return {
            "total_requests": total_requests,
            "open_requests": open_requests,
            "resolved_requests": resolved_requests,
            "total_answers": total_answers,
            "accepted_answers": accepted_answers,
            "top_helpers": top_helpers,
            "recent_requests": recent_requests,
        }
