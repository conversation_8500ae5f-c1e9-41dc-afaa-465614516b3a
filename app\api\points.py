"""
积分系统API接口
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import Optional, List
from pydantic import BaseModel, Field
import logging

from app.models.user import User
from app.services.points_service import PointsService
from app.core.permissions import get_current_user, get_current_admin_user
from app.models.auth_models import ApiResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/points", tags=["积分系统"])


class PointsHistoryResponse(BaseModel):
    """积分历史响应模型"""

    total: int
    page: int
    size: int
    pages: int
    transactions: List[dict]


class PointsAdjustRequest(BaseModel):
    """积分调整请求模型"""

    user_id: int = Field(..., description="用户ID")
    amount: int = Field(..., description="调整数量（正数为增加，负数为减少）")
    description: str = Field(..., description="调整原因")


class UserPointsResponse(BaseModel):
    """用户积分响应模型"""

    user_id: int
    username: str
    nickname: Optional[str]
    points: int
    title: str


@router.get("/my", response_model=ApiResponse)
async def get_my_points(current_user: User = Depends(get_current_user)):
    """获取当前用户积分信息"""
    try:
        user_data = UserPointsResponse(
            user_id=current_user.id,
            username=current_user.username,
            nickname=current_user.nickname,
            points=current_user.points,
            title=PointsService.get_title_by_points(current_user.points),
        )

        return ApiResponse(
            status="success", message="获取积分信息成功", data=user_data.model_dump()
        )
    except Exception as e:
        logger.error(f"获取用户积分信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取积分信息失败"
        )


@router.get("/history", response_model=ApiResponse)
async def get_points_history(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    transaction_type: Optional[str] = Query(None, description="交易类型筛选"),
    current_user: User = Depends(get_current_user),
):
    """获取当前用户积分历史"""
    try:
        history_data = await PointsService.get_user_points_history(
            user=current_user, page=page, size=size, transaction_type=transaction_type
        )

        # 转换交易记录为字典格式
        transactions_dict = []
        for transaction in history_data["transactions"]:
            transactions_dict.append(
                {
                    "id": transaction.id,
                    "amount": transaction.amount,
                    "balance_after": transaction.balance_after,
                    "transaction_type": transaction.transaction_type,
                    "description": transaction.description,
                    "related_id": transaction.related_id,
                    "created_at": transaction.created_at.isoformat(),
                }
            )

        response_data = PointsHistoryResponse(
            total=history_data["total"],
            page=history_data["page"],
            size=history_data["size"],
            pages=history_data["pages"],
            transactions=transactions_dict,
        )

        return ApiResponse(
            status="success",
            message="获取积分历史成功",
            data=response_data.model_dump(),
        )
    except Exception as e:
        logger.error(f"获取积分历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取积分历史失败"
        )


@router.get("/statistics", response_model=ApiResponse)
async def get_points_statistics(current_user: User = Depends(get_current_admin_user)):
    """获取积分系统统计信息（管理员专用）"""
    try:
        stats = await PointsService.get_points_statistics()

        return ApiResponse(status="success", message="获取积分统计成功", data=stats)
    except Exception as e:
        logger.error(f"获取积分统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取积分统计失败"
        )


@router.post("/adjust", response_model=ApiResponse)
async def adjust_user_points(
    request: PointsAdjustRequest, current_user: User = Depends(get_current_admin_user)
):
    """管理员调整用户积分"""
    try:
        # 获取目标用户
        target_user = await User.get_or_none(id=request.user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 调整积分
        transaction = await PointsService.admin_adjust_points(
            user=target_user,
            amount=request.amount,
            description=request.description,
            admin_user=current_user,
        )

        return ApiResponse(
            status="success",
            message="积分调整成功",
            data={
                "transaction_id": transaction.id,
                "user_id": target_user.id,
                "username": target_user.username,
                "amount": request.amount,
                "new_balance": target_user.points,
                "description": request.description,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"调整用户积分失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="调整积分失败"
        )


@router.get("/leaderboard", response_model=ApiResponse)
async def get_points_leaderboard(
    limit: int = Query(10, ge=1, le=50, description="排行榜数量")
):
    """获取积分排行榜"""
    try:
        top_users = await User.all().order_by("-points").limit(limit)

        leaderboard = []
        for user in top_users:
            leaderboard.append(
                {
                    "user_id": user.id,
                    "username": user.username,
                    "nickname": user.nickname,
                    "points": user.points,
                    "title": PointsService.get_title_by_points(user.points),
                }
            )

        return ApiResponse(
            status="success",
            message="获取积分排行榜成功",
            data={"leaderboard": leaderboard},
        )
    except Exception as e:
        import traceback

        logger.error(f"获取积分排行榜失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取积分排行榜失败: {str(e)}",
        )


@router.get("/rules", response_model=ApiResponse)
async def get_points_rules():
    """获取积分规则说明"""
    rules = {
        "register": {
            "amount": PointsService.POINTS_RULES["register"],
            "description": "新用户注册奖励",
        },
        "help_request": {
            "amount": PointsService.POINTS_RULES["help_request"],
            "description": "发布求助扣费",
        },
        "help_answer": {
            "amount": PointsService.POINTS_RULES["help_answer"],
            "description": "回答问题奖励",
        },
        "answer_accepted": {
            "amount": PointsService.POINTS_RULES["answer_accepted"],
            "description": "回答被采纳奖励（回答者和求助者各得此分数）",
        },
    }

    titles = {
        "新手": {"min_points": 0, "max_points": 49},
        "初级": {"min_points": 50, "max_points": 199},
        "中级": {"min_points": 200, "max_points": 499},
        "高级": {"min_points": 500, "max_points": 999},
        "专家": {"min_points": 1000, "max_points": 2499},
        "大师": {"min_points": 2500, "max_points": None},
    }

    return ApiResponse(
        status="success",
        message="获取积分规则成功",
        data={"points_rules": rules, "title_system": titles},
    )
